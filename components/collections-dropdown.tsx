"use client";

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, PlusCircle, Bookmark, Globe, Lock, X } from "lucide-react";
import { Collection } from "@/lib/types";
import { toast } from "sonner";
import { getUserCollectionsForDialog, createCollection, getPromptCollectionMembership } from "@/lib/api-services";
import { updatePromptCollections } from "@/lib/api-services/updatePromptCollections";

// Cache for collections data to avoid repeated fetches
const collectionsCache = new Map<string, { data: Collection[]; timestamp: number }>();
const membershipCache = new Map<string, { data: string[]; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Debounce utility
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
}

interface CollectionsDropdownProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  promptId: string;
  promptTitle: string;
  savedCollectionIds?: string[]; // NEW: Pre-loaded collection IDs from prompt data
  onSuccess?: (isSaved: boolean) => void;
  children: React.ReactNode; // The bookmark button trigger
}

export default function CollectionsDropdown({
  isOpen,
  onOpenChange,
  promptId,
  promptTitle,
  savedCollectionIds,
  onSuccess,
  children
}: CollectionsDropdownProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedCollectionIds, setSelectedCollectionIds] = useState<string[]>([]);
  const [initialCollectionIds, setInitialCollectionIds] = useState<string[]>([]);
  const [newCollectionName, setNewCollectionName] = useState("");
  const [newCollectionIsPublic, setNewCollectionIsPublic] = useState(false);
  const [isCreatingCollection, setIsCreatingCollection] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [user, setUser] = useState<any>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Fetch user session
  useEffect(() => {
    const fetchUser = async () => {
      const { createBrowserClient } = await import("@supabase/ssr");
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
    };
    fetchUser();
  }, []);

  // Cached fetch functions
  const fetchCollectionsWithCache = useCallback(async (userId: string, signal?: AbortSignal): Promise<Collection[]> => {
    const cacheKey = userId;
    const cached = collectionsCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.data;
    }

    const collections = await getUserCollectionsForDialog(userId, signal);
    collectionsCache.set(cacheKey, { data: collections, timestamp: Date.now() });
    return collections;
  }, []);

  const fetchMembershipWithCache = useCallback(async (userId: string, promptId: string, signal?: AbortSignal): Promise<string[]> => {
    const cacheKey = `${userId}-${promptId}`;
    const cached = membershipCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.data;
    }

    const membership = await getPromptCollectionMembership(userId, promptId, signal);
    membershipCache.set(cacheKey, { data: membership.collectionIds, timestamp: Date.now() });
    return membership.collectionIds;
  }, []);

  // Debounced fetch function to prevent rapid successive calls
  const debouncedFetchCollectionsData = useMemo(
    () => debounce(async () => {
      if (!isOpen || !user?.id) return;

      setIsLoading(true);

      try {
        // Cancel any previous request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }

        const abortController = new AbortController();
        abortControllerRef.current = abortController;

        // Fetch collections and use pre-loaded membership data if available
        const collections = await fetchCollectionsWithCache(user.id, abortController.signal);

        // Use savedCollectionIds from props if available, otherwise fetch from cache/API
        const membershipIds = savedCollectionIds && savedCollectionIds.length > 0
          ? savedCollectionIds
          : await fetchMembershipWithCache(user.id, promptId, abortController.signal);

        if (abortController.signal.aborted) return;

        setCollections(collections);
        setSelectedCollectionIds(membershipIds);
        setInitialCollectionIds(membershipIds);
      } catch (error: any) {
        if (error.name === 'AbortError') return;
        console.error('Error fetching collections:', error);
        toast.error('Failed to load collections');
      } finally {
        setIsLoading(false);
      }
    }, 300),
    [isOpen, user?.id, promptId, fetchCollectionsWithCache, fetchMembershipWithCache]
  );

  // Fetch collections data when dropdown opens
  useEffect(() => {
    debouncedFetchCollectionsData();

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [debouncedFetchCollectionsData]);

  // Reset state when dropdown closes
  useEffect(() => {
    if (!isOpen) {
      setNewCollectionName("");
      setNewCollectionIsPublic(false);
      setShowCreateForm(false);
      setIsCreatingCollection(false);
    }
  }, [isOpen]);

  const handleToggleCollection = async (collectionId: string) => {
    if (!user?.id) return;

    const isCurrentlySelected = selectedCollectionIds.includes(collectionId);
    const newSelectedIds = isCurrentlySelected
      ? selectedCollectionIds.filter(id => id !== collectionId)
      : [...selectedCollectionIds, collectionId];

    // Optimistically update UI
    setSelectedCollectionIds(newSelectedIds);

    try {
      // Determine the action needed
      const collectionsToAddTo = isCurrentlySelected ? [] : [collectionId];
      const collectionsToRemoveFrom = isCurrentlySelected ? [collectionId] : [];

      const result = await updatePromptCollections(user.id, promptId, {
        addToCollectionIds: collectionsToAddTo,
        removeFromCollectionIds: collectionsToRemoveFrom
      });

      if (result.success) {
        const collectionName = collections.find(c => c.id === collectionId)?.name || 'collection';
        toast.success(isCurrentlySelected
          ? `Removed from ${collectionName}`
          : `Added to ${collectionName}`
        );

        // Update initial state to reflect the change
        setInitialCollectionIds(newSelectedIds);

        // Invalidate membership cache for this prompt
        const membershipCacheKey = `${user.id}-${promptId}`;
        membershipCache.delete(membershipCacheKey);

        // Notify parent of save status change
        if (onSuccess) {
          onSuccess(newSelectedIds.length > 0);
        }
      } else {
        // Revert optimistic update on failure
        setSelectedCollectionIds(selectedCollectionIds);
        toast.error('Failed to update collection');
      }
    } catch (error) {
      // Revert optimistic update on error
      setSelectedCollectionIds(selectedCollectionIds);
      toast.error('An error occurred');
      console.error('Error toggling collection:', error);
    }
  };

  const handleCreateCollection = async () => {
    if (!user?.id || !newCollectionName.trim() || newCollectionName.trim().length < 3) {
      toast.error("Collection name must be at least 3 characters");
      return;
    }

    setIsCreatingCollection(true);
    try {
      const newCollection = await createCollection(user.id, {
        name: newCollectionName.trim(),
        is_public: newCollectionIsPublic
      });

      // Add to collections list
      const newCollectionWithDefaults = {
        ...newCollection,
        id: newCollection.id,
        name: newCollection.name,
        userId: newCollection.userId || user.id,
        description: newCollection.description || "",
        icon: newCollection.icon || "",
        isPublic: newCollection.isPublic || newCollectionIsPublic,
        isDefault: newCollection.isDefault || false,
        promptCount: newCollection.promptCount || 0,
        viewCount: newCollection.viewCount || 0,
        createdAt: newCollection.createdAt || new Date().toISOString(),
        updatedAt: newCollection.updatedAt || new Date().toISOString()
      };

      setCollections(prev => [newCollectionWithDefaults, ...prev]);

      // Invalidate collections cache since we added a new collection
      const collectionsCacheKey = user.id;
      collectionsCache.delete(collectionsCacheKey);

      // Automatically add prompt to new collection using the proper toggle function
      // This will use updatePromptCollections instead of the problematic addPromptToCollection
      const newSelectedIds = [...selectedCollectionIds, newCollectionWithDefaults.id];
      setSelectedCollectionIds(newSelectedIds);

      try {
        const result = await updatePromptCollections(user.id, promptId, {
          addToCollectionIds: [newCollectionWithDefaults.id],
          removeFromCollectionIds: []
        });

        if (result.success) {
          setInitialCollectionIds(newSelectedIds);
          // Invalidate membership cache
          const membershipCacheKey = `${user.id}-${promptId}`;
          membershipCache.delete(membershipCacheKey);

          if (onSuccess) {
            onSuccess(newSelectedIds.length > 0);
          }
        } else {
          // Revert if adding to collection failed
          setSelectedCollectionIds(selectedCollectionIds);
          toast.error('Created collection but failed to add prompt');
        }
      } catch (addError) {
        // Revert if adding to collection failed
        setSelectedCollectionIds(selectedCollectionIds);
        console.error('Error adding prompt to new collection:', addError);
        toast.error('Created collection but failed to add prompt');
      }

      setNewCollectionName("");
      setNewCollectionIsPublic(false);
      setShowCreateForm(false);
      toast.success(`Created "${newCollectionWithDefaults.name}"`);
    } catch (error) {
      console.error('Error creating collection:', error);
      toast.error('Failed to create collection');
    } finally {
      setIsCreatingCollection(false);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end" side="bottom">
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-sm">Save to Collection</h4>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => onOpenChange(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <>
              <ScrollArea className="max-h-60">
                <div className="space-y-1">
                  {collections.map((collection) => {
                    const isSelected = selectedCollectionIds.includes(collection.id);
                    return (
                      <div
                        key={collection.id}
                        className={`flex items-center justify-between p-2 rounded-md hover:bg-accent cursor-pointer ${
                          isSelected ? 'bg-accent-green/10 border border-accent-green/20' : ''
                        }`}
                        onClick={() => handleToggleCollection(collection.id)}
                      >
                        <div className="flex items-center gap-2 flex-1">
                          {collection.isPublic ? (
                            <Globe className="h-3 w-3 text-muted-foreground" />
                          ) : (
                            <Lock className="h-3 w-3 text-muted-foreground" />
                          )}
                          {collection.isDefault && (
                            <Bookmark className="h-3 w-3 text-accent-blue" />
                          )}
                          <span className={`text-sm ${isSelected ? 'font-medium' : ''}`}>
                            {collection.name}
                          </span>
                        </div>
                        <Checkbox
                          checked={isSelected}
                          onChange={() => {}} // Handled by parent div click
                        />
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>

              <div className="mt-3 pt-3 border-t">
                {!showCreateForm ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-muted-foreground"
                    onClick={() => setShowCreateForm(true)}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Create new collection
                  </Button>
                ) : (
                  <div className="space-y-3">
                    <Input
                      placeholder="Collection name..."
                      value={newCollectionName}
                      onChange={(e) => setNewCollectionName(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleCreateCollection();
                        } else if (e.key === 'Escape') {
                          setShowCreateForm(false);
                          setNewCollectionName("");
                          setNewCollectionIsPublic(false);
                        }
                      }}
                      disabled={isCreatingCollection}
                      autoFocus
                    />

                    {/* Public/Private Toggle */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id="collection-public"
                          checked={newCollectionIsPublic}
                          onCheckedChange={(checked) => setNewCollectionIsPublic(!!checked)}
                          disabled={isCreatingCollection}
                        />
                        <label
                          htmlFor="collection-public"
                          className="text-sm font-medium cursor-pointer"
                        >
                          Make public
                        </label>
                      </div>
                      <div className="flex items-center gap-1">
                        {newCollectionIsPublic ? (
                          <Globe className="h-3 w-3 text-muted-foreground" />
                        ) : (
                          <Lock className="h-3 w-3 text-muted-foreground" />
                        )}
                        <span className="text-xs text-muted-foreground">
                          {newCollectionIsPublic ? 'Public' : 'Private'}
                        </span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={handleCreateCollection}
                        disabled={!newCollectionName.trim() || newCollectionName.trim().length < 3 || isCreatingCollection}
                        className="flex-1"
                      >
                        {isCreatingCollection ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          'Create'
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setShowCreateForm(false);
                          setNewCollectionName("");
                          setNewCollectionIsPublic(false);
                        }}
                        disabled={isCreatingCollection}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
