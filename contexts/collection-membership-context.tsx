"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { useUser } from '@/lib/hooks/use-user'
import { getUserCollectionsForDialog } from '@/lib/api-services'
import type { Collection } from '@/lib/types'

interface CollectionMembershipContextType {
  collections: Collection[]
  isLoadingCollections: boolean
  membershipData: Record<string, string[]> // promptId -> collectionIds
  loadPromptMembership: (promptId: string, savedCollectionIds?: string[]) => Promise<string[]>
  invalidatePromptMembership: (promptId: string) => void
  invalidateAllCaches: () => void
  refreshCollections: () => Promise<void>
}

const CollectionMembershipContext = createContext<CollectionMembershipContextType | null>(null)

interface CollectionMembershipProviderProps {
  children: React.ReactNode
}

export function CollectionMembershipProvider({ children }: CollectionMembershipProviderProps) {
  const { user } = useUser()
  const [collections, setCollections] = useState<Collection[]>([])
  const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  const [membershipData, setMembershipData] = useState<Record<string, string[]>>({})

  // Load user's collections
  const loadCollections = useCallback(async () => {
    if (!user?.id || isLoadingCollections) return

    setIsLoadingCollections(true)
    try {
      const userCollections = await getUserCollectionsForDialog(user.id)
      setCollections(userCollections)
    } catch (error) {
      console.error('[CollectionMembershipProvider] Error loading collections:', error)
    } finally {
      setIsLoadingCollections(false)
    }
  }, [user?.id, isLoadingCollections])

  // Load collection membership for a specific prompt
  const loadPromptMembership = useCallback(async (
    promptId: string, 
    savedCollectionIds?: string[]
  ): Promise<string[]> => {
    if (!user?.id) return []

    // If we already have saved collection IDs from the prompt data, use them
    if (savedCollectionIds && savedCollectionIds.length > 0) {
      setMembershipData(prev => ({
        ...prev,
        [promptId]: savedCollectionIds
      }))
      return savedCollectionIds
    }

    // Check if we already have cached data
    const cached = membershipData[promptId]
    if (cached) {
      return cached
    }

    try {
      // For now, we'll use the savedCollectionIds from the prompt data
      // In the future, we could implement a separate API call here if needed
      const collectionIds = savedCollectionIds || []
      
      setMembershipData(prev => ({
        ...prev,
        [promptId]: collectionIds
      }))
      
      return collectionIds
    } catch (error) {
      console.error('[CollectionMembershipProvider] Error loading prompt membership:', error)
      return []
    }
  }, [user?.id, membershipData])

  // Invalidate cache for a specific prompt
  const invalidatePromptMembership = useCallback((promptId: string) => {
    setMembershipData(prev => {
      const newData = { ...prev }
      delete newData[promptId]
      return newData
    })
  }, [])

  // Invalidate all caches
  const invalidateAllCaches = useCallback(() => {
    setMembershipData({})
    setCollections([])
  }, [])

  // Refresh collections
  const refreshCollections = useCallback(async () => {
    await loadCollections()
  }, [loadCollections])

  // Load collections when user changes
  useEffect(() => {
    if (user?.id) {
      loadCollections()
    } else {
      // Clear data when user logs out
      setCollections([])
      setMembershipData({})
    }
  }, [user?.id, loadCollections])

  const value: CollectionMembershipContextType = {
    collections,
    isLoadingCollections,
    membershipData,
    loadPromptMembership,
    invalidatePromptMembership,
    invalidateAllCaches,
    refreshCollections
  }

  return (
    <CollectionMembershipContext.Provider value={value}>
      {children}
    </CollectionMembershipContext.Provider>
  )
}

export function useCollectionMembershipContext() {
  const context = useContext(CollectionMembershipContext)
  if (!context) {
    throw new Error('useCollectionMembershipContext must be used within a CollectionMembershipProvider')
  }
  return context
}
