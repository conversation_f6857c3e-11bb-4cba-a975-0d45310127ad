"use client"

import { useState, useEffect, useCallback } from "react"
import { getUserCollectionsForDialog, getPromptCollectionMembership } from "@/lib/api-services"
import type { Collection } from "@/lib/types"

interface CollectionMembershipCache {
  [promptId: string]: {
    collectionIds: string[]
    timestamp: number
  }
}

interface CollectionsCache {
  collections: Collection[]
  timestamp: number
}

// Cache with 5-minute TTL
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes
let collectionsCache: CollectionsCache | null = null
let membershipCache: CollectionMembershipCache = {}

export function useCollectionMembership(userId?: string) {
  const [collections, setCollections] = useState<Collection[]>([])
  const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  const [membershipData, setMembershipData] = useState<CollectionMembershipCache>({})

  // Load user's collections in the background
  const loadCollections = useCallback(async () => {
    if (!userId || isLoadingCollections) return

    // Check cache first
    if (collectionsCache && Date.now() - collectionsCache.timestamp < CACHE_TTL) {
      setCollections(collectionsCache.collections)
      return collectionsCache.collections
    }

    setIsLoadingCollections(true)
    try {
      const userCollections = await getUserCollectionsForDialog(userId)
      
      // Update cache
      collectionsCache = {
        collections: userCollections,
        timestamp: Date.now()
      }
      
      setCollections(userCollections)
      return userCollections
    } catch (error) {
      console.error('[useCollectionMembership] Error loading collections:', error)
      return []
    } finally {
      setIsLoadingCollections(false)
    }
  }, [userId, isLoadingCollections])

  // Load collection membership for a specific prompt
  const loadPromptMembership = useCallback(async (promptId: string): Promise<string[]> => {
    if (!userId) return []

    // Check cache first
    const cached = membershipCache[promptId]
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.collectionIds
    }

    try {
      const { collectionIds } = await getPromptCollectionMembership(userId, promptId)
      
      // Update cache
      membershipCache[promptId] = {
        collectionIds,
        timestamp: Date.now()
      }
      
      // Update state
      setMembershipData(prev => ({
        ...prev,
        [promptId]: {
          collectionIds,
          timestamp: Date.now()
        }
      }))
      
      return collectionIds
    } catch (error) {
      console.error('[useCollectionMembership] Error loading prompt membership:', error)
      return []
    }
  }, [userId])

  // Get cached membership data for a prompt
  const getPromptMembershipFromCache = useCallback((promptId: string): string[] => {
    const cached = membershipCache[promptId]
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.collectionIds
    }
    return []
  }, [])

  // Invalidate cache for a specific prompt (useful after save/unsave operations)
  const invalidatePromptMembership = useCallback((promptId: string) => {
    delete membershipCache[promptId]
    setMembershipData(prev => {
      const newData = { ...prev }
      delete newData[promptId]
      return newData
    })
  }, [])

  // Invalidate all caches (useful after collection changes)
  const invalidateAllCaches = useCallback(() => {
    collectionsCache = null
    membershipCache = {}
    setMembershipData({})
    setCollections([])
  }, [])

  // Load collections on mount if user is available
  useEffect(() => {
    if (userId) {
      loadCollections()
    }
  }, [userId, loadCollections])

  return {
    collections,
    isLoadingCollections,
    membershipData,
    loadCollections,
    loadPromptMembership,
    getPromptMembershipFromCache,
    invalidatePromptMembership,
    invalidateAllCaches
  }
}
