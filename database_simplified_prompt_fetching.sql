-- Simplified Prompt Fetching: Single unified function to replace all existing complexity
-- This replaces get_prompts_with_saved_status, prompt_card_details view, and eliminates fallback logic

-- Step 1: Drop existing functions and views to avoid conflicts
DROP FUNCTION IF EXISTS get_prompts_with_saved_status CASCADE;
DROP VIEW IF EXISTS prompt_card_details_with_saved CASCADE;
DROP VIEW IF EXISTS prompt_card_details CASCADE;

-- Step 2: Create a single, unified function that handles all prompt fetching scenarios
CREATE OR REPLACE FUNCTION get_prompts_unified(
  p_user_id uuid DEFAULT NULL,
  p_limit integer DEFAULT 20,
  p_offset integer DEFAULT 0,
  p_category_slugs text[] DEFAULT NULL,
  p_tool_slugs text[] DEFAULT NULL,
  p_tag_slugs text[] DEFAULT NULL,
  p_ai_model_slugs text[] DEFAULT NULL,
  p_search_query text DEFAULT NULL,
  p_author_id uuid DEFAULT NULL,
  p_sort_by text DEFAULT 'created_at',
  p_sort_order text DEFAULT 'desc'
)
RETURNS TABLE (
  id uuid,
  short_id text,
  title text,
  description text,
  image_url text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  is_public boolean,
  view_count integer,
  primary_tag_id integer,
  category_id integer,
  tool_id integer,
  author_id uuid,
  search_vector tsvector,
  tag_slugs_array text[],
  category_name text,
  category_slug text,
  tool_name text,
  tool_slug text,
  author_username text,
  author_avatar_url text,
  primary_tag_slug text,
  tags jsonb,
  rating bigint,
  comment_count bigint,
  trending_score double precision,
  ai_model_id integer,
  ai_model_provider text,
  ai_model_name text,
  ai_model_slug text,
  ai_model_deprecated boolean,
  is_saved_by_user boolean,
  is_owned_by_user boolean,
  saved_collection_ids uuid[],
  remix_count bigint
) AS $$
DECLARE
  search_ts_query tsquery;
  exact_username_match boolean := false;
BEGIN
  -- Prepare search query if provided
  IF p_search_query IS NOT NULL AND p_search_query != '' THEN
    search_ts_query := plainto_tsquery('english', p_search_query);
    -- Check if this is an exact username search
    exact_username_match := EXISTS (
      SELECT 1 FROM profiles 
      WHERE LOWER(username) = LOWER(p_search_query)
    );
  END IF;

  RETURN QUERY
  WITH aggregated_tags AS (
    SELECT 
      pt.prompt_id,
      jsonb_agg(
        jsonb_build_object('id', t.id, 'name', t.name, 'slug', t.slug) 
        ORDER BY t.name
      ) FILTER (WHERE t.id IS NOT NULL) AS tags_jsonb,
      array_agg(t.slug ORDER BY t.name) FILTER (WHERE t.slug IS NOT NULL) AS tag_slugs_array
    FROM prompt_tags pt
    JOIN tags t ON pt.tag_id = t.id
    GROUP BY pt.prompt_id
  ),
  user_saved_status AS (
    SELECT
      cp.prompt_id,
      array_agg(cp.collection_id) AS collection_ids
    FROM collection_prompts cp
    JOIN collections c ON cp.collection_id = c.id
    WHERE c.user_id = p_user_id
    GROUP BY cp.prompt_id
  ),
  prompt_stats AS (
    SELECT
      ps.id,
      COALESCE(ps.rating, 0) AS rating,
      COALESCE(ps.comment_count, 0) AS comment_count,
      COALESCE(ps.remix_count, 0) AS remix_count
    FROM prompt_statistics ps
  ),
  trending_data AS (
    SELECT
      td.id,
      COALESCE(td.trending_score, 0) AS trending_score
    FROM trending_prompts td
  )
  SELECT 
    p.id,
    p.short_id,
    p.title,
    p.description,
    p.image_url,
    p.created_at,
    p.updated_at,
    p.is_public,
    p.view_count,
    p.primary_tag_id,
    p.category_id,
    p.tool_id,
    p.user_id AS author_id,
    p.search_vector,
    COALESCE(at.tag_slugs_array, '{}') AS tag_slugs_array,
    cat.name AS category_name,
    cat.slug AS category_slug,
    tool.name AS tool_name,
    tool.slug AS tool_slug,
    prof.username AS author_username,
    prof.avatar_url AS author_avatar_url,
    ptag.slug AS primary_tag_slug,
    COALESCE(at.tags_jsonb, '[]'::jsonb) AS tags,
    COALESCE(ps.rating, 0) AS rating,
    COALESCE(ps.comment_count, 0) AS comment_count,
    COALESCE(td.trending_score, 0) AS trending_score,
    p.ai_model_id,
    am.provider AS ai_model_provider,
    am.tool_name AS ai_model_name,
    am.slug AS ai_model_slug,
    am.deprecated AS ai_model_deprecated,
    -- Always include is_saved_by_user field (false for anonymous users)
    CASE
      WHEN p_user_id IS NULL THEN false
      WHEN uss.prompt_id IS NOT NULL THEN true
      ELSE false
    END AS is_saved_by_user,
    -- Always include is_owned_by_user field (false for anonymous users)
    CASE
      WHEN p_user_id IS NULL THEN false
      WHEN p.user_id = p_user_id THEN true
      ELSE false
    END AS is_owned_by_user,
    -- Include collection IDs for saved prompts (empty array if not saved)
    COALESCE(uss.collection_ids, '{}') AS saved_collection_ids,
    COALESCE(ps.remix_count, 0) AS remix_count
  FROM prompts p
  LEFT JOIN categories cat ON p.category_id = cat.id
  LEFT JOIN tools tool ON p.tool_id = tool.id
  LEFT JOIN profiles prof ON p.user_id = prof.id
  LEFT JOIN tags ptag ON p.primary_tag_id = ptag.id
  LEFT JOIN aggregated_tags at ON p.id = at.prompt_id
  LEFT JOIN user_saved_status uss ON p.id = uss.prompt_id
  LEFT JOIN prompt_stats ps ON p.id = ps.id
  LEFT JOIN trending_data td ON p.id = td.id
  LEFT JOIN ai_models am ON p.ai_model_id = am.id
  WHERE 
    p.is_public = true
    AND (p_category_slugs IS NULL OR cat.slug = ANY(p_category_slugs))
    AND (p_tool_slugs IS NULL OR tool.slug = ANY(p_tool_slugs))
    AND (p_tag_slugs IS NULL OR at.tag_slugs_array && p_tag_slugs)
    AND (p_ai_model_slugs IS NULL OR am.slug = ANY(p_ai_model_slugs))
    AND (p_author_id IS NULL OR p.user_id = p_author_id)
    AND (
      p_search_query IS NULL 
      OR p.search_vector @@ search_ts_query
      OR prof.username ILIKE '%' || p_search_query || '%'
    )
  ORDER BY
    -- Handle different sort options
    CASE WHEN p_sort_by = 'created_at' AND p_sort_order = 'desc' THEN p.created_at END DESC,
    CASE WHEN p_sort_by = 'created_at' AND p_sort_order = 'asc' THEN p.created_at END ASC,
    CASE WHEN p_sort_by = 'trending_score' AND p_sort_order = 'desc' THEN td.trending_score END DESC,
    CASE WHEN p_sort_by = 'trending_score' AND p_sort_order = 'asc' THEN td.trending_score END ASC,
    CASE WHEN p_sort_by = 'rating' AND p_sort_order = 'desc' THEN ps.rating END DESC,
    CASE WHEN p_sort_by = 'rating' AND p_sort_order = 'asc' THEN ps.rating END ASC,
    CASE WHEN p_sort_by = 'view_count' AND p_sort_order = 'desc' THEN p.view_count END DESC,
    CASE WHEN p_sort_by = 'view_count' AND p_sort_order = 'asc' THEN p.view_count END ASC,
    -- Default fallback
    p.created_at DESC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE;

-- Step 3: Grant permissions
GRANT EXECUTE ON FUNCTION get_prompts_unified TO authenticated;
GRANT EXECUTE ON FUNCTION get_prompts_unified TO anon;

-- Step 4: Add performance indexes
CREATE INDEX IF NOT EXISTS idx_prompts_public_created_at ON prompts (is_public, created_at DESC) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_prompts_public_trending ON prompts (is_public) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_collection_prompts_user_prompt ON collection_prompts (prompt_id);
CREATE INDEX IF NOT EXISTS idx_collections_user_id ON collections (user_id);

-- Step 5: Add helpful comments
COMMENT ON FUNCTION get_prompts_unified IS 'Unified function for fetching prompts with saved status. Handles all filtering, sorting, and saved status calculation in a single query. Always returns is_saved_by_user field (false for anonymous users).';

-- Step 6: Create a simple view for backward compatibility (optional)
CREATE OR REPLACE VIEW prompt_card_details AS
SELECT
  id,
  short_id,
  title,
  description,
  image_url,
  created_at,
  updated_at,
  is_public,
  view_count,
  primary_tag_id,
  category_id,
  tool_id,
  author_id,
  search_vector,
  tag_slugs_array,
  category_name,
  category_slug,
  tool_name,
  tool_slug,
  author_username,
  author_avatar_url,
  primary_tag_slug,
  tags,
  rating,
  comment_count,
  trending_score,
  ai_model_id,
  ai_model_provider,
  ai_model_name,
  ai_model_slug,
  ai_model_deprecated,
  is_saved_by_user,
  is_owned_by_user,
  saved_collection_ids,
  remix_count
FROM get_prompts_unified(NULL, 1000, 0);

COMMENT ON VIEW prompt_card_details IS 'Backward compatibility view. Use get_prompts_unified function directly for better performance.';
