-- Performance Improvements: Enhanced Prompt Fetching with Ownership and Collection Membership
-- This script applies the database changes needed for the performance improvements

-- STEP 1: First, apply the enhanced get_prompts_unified function
-- You need to run the contents of database_simplified_prompt_fetching.sql first
-- Either use: \i database_simplified_prompt_fetching.sql
-- Or copy and paste the entire contents of that file and execute it

-- STEP 2: After applying the enhanced function, run the tests below

-- Verify the function was created successfully
SELECT 
  routine_name,
  routine_type,
  data_type
FROM information_schema.routines 
WHERE routine_name = 'get_prompts_unified' 
  AND routine_schema = 'public';

-- Test the function with a sample query
SELECT 
  id,
  title,
  is_saved_by_user,
  is_owned_by_user,
  saved_collection_ids
FROM get_prompts_unified(
  p_user_id := NULL, -- Test with anonymous user
  p_limit := 5,
  p_offset := 0
)
LIMIT 5;

-- Test with a real user ID (replace with actual UUID)
-- SELECT 
--   id,
--   title,
--   is_saved_by_user,
--   is_owned_by_user,
--   saved_collection_ids
-- FROM get_prompts_unified(
--   p_user_id := 'your-user-uuid-here',
--   p_limit := 5,
--   p_offset := 0
-- )
-- LIMIT 5;

COMMENT ON FUNCTION get_prompts_unified IS 'Enhanced function for fetching prompts with saved status, ownership determination, and collection membership. Eliminates need for secondary fetches and reduces re-renders.';

-- Create an index to optimize the new collection membership query
CREATE INDEX IF NOT EXISTS idx_collection_prompts_prompt_collection 
ON collection_prompts (prompt_id, collection_id);

-- Analyze the tables to update statistics for the query planner
ANALYZE prompts;
ANALYZE collections;
ANALYZE collection_prompts;
ANALYZE profiles;

-- Show the execution plan for the function (for performance analysis)
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM get_prompts_unified(NULL, 10, 0) LIMIT 10;
